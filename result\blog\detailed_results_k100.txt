============================================================
离散Nelder-Mead算法详细结果报告
============================================================

【基本信息】
网络名称: blog
种子集合大小: 100
单纯形维度: 30
传播概率: 0.05
最大迭代次数: 1
算法参数: α=0.5, γ=1.5, ρ=1.0, σ=0.375
PRE递推轮数: 5

【最终结果】
全局最优适应度: 221.528059
最优解发现代数: 0
IC模型估计影响力: 220.03
蒙特卡洛模拟次数: 1000
总运行时间: 87.58秒
最优种子集合: [10, 11, 20, 41, 44, 64, 73, 94, 106, 108, 119, 124, 126, 129, 141, 143, 152, 169, 171, 175, 178, 184, 197, 201, 232, 235, 236, 237, 243, 249, 264, 307, 373, 391, 411, 416, 430, 439, 444, 445, 448, 485, 492, 497, 508, 554, 581, 591, 610, 623, 661, 698, 758, 763, 776, 820, 826, 866, 872, 920, 924, 944, 1087, 1119, 1169, 1171, 1190, 1246, 1262, 1280, 1301, 1380, 1455, 1464, 1501, 1507, 1544, 1565, 1588, 1592, 1627, 1628, 1674, 1819, 1836, 2107, 2369, 2534, 2547, 2629, 2844, 3107, 3242, 3320, 3333, 3432, 3492, 3552, 3698, 3954]

【进化历史概览】
初始适应度: 213.974138
最终适应度: 213.974138
适应度提升: 0.000000
总改进次数: 0

【每代进化详情】
代数	当前最优	全局最优	平均值	标准差	是否更新
------------------------------------------------------------
0	213.9741	213.9741	199.7678	7.8021	否

============================================================
报告生成完成

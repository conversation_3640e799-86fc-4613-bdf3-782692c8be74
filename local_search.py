
"""
局部搜索算法模块
提供高效的并行局部搜索优化功能，支持缓存机制
"""

from typing import Set, List, Dict, Optional, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from base_fun import PRE

def local_search(xi: Union[Set[int], List[int]], G, p: float, k: int,
                neighbors: Optional[Dict[int, List[int]]] = None,
                max_hop: int = 5,
                fitness_cache: Optional[Dict] = None,
                n_jobs: int = 4) -> List[int]:
    """
    并行局部搜索算法，优化种子节点集合（带缓存机制）

    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        neighbors: 邻居字典，格式为 {node: [neighbors]}
        max_hop: PRE递推轮数
        fitness_cache: 适应度缓存字典，用于避免重复计算
        n_jobs: 并行线程数

    Returns:
        list: 优化后的种子集合
    """
    # 如果没有提供neighbors字典，则生成一个
    if neighbors is None:
        neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

    # 确保xi是集合类型
    if not isinstance(xi, set):
        xi = set(xi)

    # 初始化缓存（如果没有提供）
    if fitness_cache is None:
        fitness_cache = {}

    # 线程锁，用于保护缓存的并发访问
    cache_lock = threading.Lock()

    def cached_PRE(seed_set: Set[int]) -> float:
        """带缓存的PRE计算函数（线程安全）"""
        cache_key = tuple(sorted(seed_set))

        # 先检查缓存（读操作，使用锁保护）
        with cache_lock:
            if cache_key in fitness_cache:
                return fitness_cache[cache_key]

        # 计算PRE值
        fitness_value = PRE(G, seed_set, p, neighbors, max_hop)

        # 更新缓存（写操作，使用锁保护）
        with cache_lock:
            fitness_cache[cache_key] = fitness_value

        return fitness_value

    # 缓存当前种子集合的影响力值
    xi_fitness = cached_PRE(xi)

    # -------------------
    # 1. 生成候选解列表
    # -------------------
    candidates = []
    for x_ij in list(xi):
        for neighbor in neighbors[x_ij]:
            if neighbor not in xi:
                xi_new = xi.copy()
                xi_new.remove(x_ij)
                xi_new.add(neighbor)
                candidates.append((x_ij, neighbor, xi_new))

    if not candidates:
        return list(xi)

    # -------------------
    # 2. 并行计算候选解的适应度
    # -------------------
    candidate_fitness = []

    # 直接使用并行计算
    if n_jobs > 1 and len(candidates) > 1:
        with ThreadPoolExecutor(max_workers=min(n_jobs, len(candidates))) as executor:
            # 提交所有任务
            future_to_index = {executor.submit(cached_PRE, cand[2]): i for i, cand in enumerate(candidates)}

            # 收集结果
            results = [None] * len(candidates)
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    fitness = future.result()
                    results[index] = fitness
                except Exception as e:
                    print(f"Error in parallel PRE calculation: {e}")
                    # 发生错误时直接计算
                    results[index] = cached_PRE(candidates[index][2])

            # 组合候选解和适应度
            candidate_fitness = list(zip(candidates, results))
    else:
        # 单线程或只有一个候选解时直接计算
        for cand in candidates:
            fitness = cached_PRE(cand[2])
            candidate_fitness.append((cand, fitness))

    # -------------------
    # 3. 找到提升最大的候选解
    # -------------------
    best_cand, best_fitness = None, xi_fitness
    for cand, fitness in candidate_fitness:
        if fitness > best_fitness:
            best_cand, best_fitness = cand, fitness

    if best_cand is not None:
        xi = best_cand[2]  # 更新为最优解

    return list(xi)


def local_search_iterative(xi: Union[Set[int], List[int]], G, p: float, k: int,
                          neighbors: Optional[Dict[int, List[int]]] = None,
                          max_hop: int = 5,
                          fitness_cache: Optional[Dict] = None,
                          max_iterations: int = 10,
                          n_jobs: int = 4) -> List[int]:
    """
    迭代并行局部搜索算法，重复执行局部搜索直到无法改进

    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        neighbors: 邻居字典
        max_hop: PRE递推轮数
        fitness_cache: 适应度缓存字典
        max_iterations: 最大迭代次数
        n_jobs: 并行线程数

    Returns:
        list: 优化后的种子集合
    """
    current_solution = list(xi) if isinstance(xi, set) else xi.copy()

    for _ in range(max_iterations):
        # 执行一轮并行局部搜索
        improved_solution = local_search(
            current_solution, G, p, k, neighbors, max_hop, fitness_cache, n_jobs
        )

        # 如果没有改进，停止迭代
        if set(improved_solution) == set(current_solution):
            break

        current_solution = improved_solution

    return current_solution


def local_search_best_improvement(xi: Union[Set[int], List[int]], G, p: float, k: int,
                                neighbors: Optional[Dict[int, List[int]]] = None,
                                max_hop: int = 5,
                                fitness_cache: Optional[Dict] = None,
                                n_jobs: int = 4) -> List[int]:
    """
    最佳改进并行局部搜索：评估所有邻居解，选择最佳的进行替换
    相比于first_improvement策略，这种方法更彻底但计算量更大

    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        neighbors: 邻居字典
        max_hop: PRE递推轮数
        fitness_cache: 适应度缓存字典
        n_jobs: 并行线程数

    Returns:
        list: 优化后的种子集合
    """
    # 如果没有提供neighbors字典，则生成一个
    if neighbors is None:
        neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

    # 确保xi是集合类型
    if not isinstance(xi, set):
        xi = set(xi)

    # 初始化缓存（如果没有提供）
    if fitness_cache is None:
        fitness_cache = {}

    # 线程锁，用于保护缓存的并发访问
    cache_lock = threading.Lock()

    def cached_PRE(seed_set: Set[int]) -> float:
        """带缓存的PRE计算函数（线程安全）"""
        cache_key = tuple(sorted(seed_set))

        # 先检查缓存（读操作，使用锁保护）
        with cache_lock:
            if cache_key in fitness_cache:
                return fitness_cache[cache_key]

        # 计算PRE值
        fitness_value = PRE(G, seed_set, p, neighbors, max_hop)

        # 更新缓存（写操作，使用锁保护）
        with cache_lock:
            fitness_cache[cache_key] = fitness_value

        return fitness_value

    # 缓存当前种子集合的影响力值
    xi_fitness = cached_PRE(xi)

    # 生成所有可能的邻居解
    all_candidates = []
    for x_ij in list(xi):
        for neighbor in neighbors[x_ij]:
            if neighbor not in xi:
                xi_new = xi.copy()
                xi_new.remove(x_ij)
                xi_new.add(neighbor)
                all_candidates.append((x_ij, neighbor, xi_new))

    if not all_candidates:
        return list(xi)

    # 并行评估所有候选解
    candidate_fitness_list = []

    # 直接使用并行计算
    if n_jobs > 1 and len(all_candidates) > 1:
        with ThreadPoolExecutor(max_workers=min(n_jobs, len(all_candidates))) as executor:
            # 提交所有任务
            future_to_index = {executor.submit(cached_PRE, cand[2]): i for i, cand in enumerate(all_candidates)}

            # 收集结果
            results = [None] * len(all_candidates)
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    fitness = future.result()
                    results[index] = fitness
                except Exception as e:
                    print(f"Error in parallel PRE calculation: {e}")
                    results[index] = cached_PRE(all_candidates[index][2])

            # 组合候选解和适应度
            candidate_fitness_list = list(zip(all_candidates, results))
    else:
        # 单线程或只有一个候选解时直接计算
        for candidate in all_candidates:
            fitness = cached_PRE(candidate[2])
            candidate_fitness_list.append((candidate, fitness))

    # 找到最佳候选解
    best_candidate = None
    best_fitness = xi_fitness

    for candidate, fitness in candidate_fitness_list:
        if fitness > best_fitness:
            best_candidate = candidate
            best_fitness = fitness

    # 如果找到更好的解，返回它
    if best_candidate is not None:
        return list(best_candidate[2])
    else:
        return list(xi)


def clear_local_search_cache(fitness_cache: Optional[Dict] = None) -> None:
    """
    清除局部搜索缓存，释放内存

    Args:
        fitness_cache: 要清除的缓存字典
    """
    if fitness_cache is not None:
        cache_size = len(fitness_cache)
        fitness_cache.clear()
        print(f"已清除局部搜索缓存 ({cache_size} 个条目)")

    # 强制垃圾回收
    import gc
    gc.collect()
